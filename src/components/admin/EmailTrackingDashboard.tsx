import React, { useState, useEffect } from 'react';
// Replace direct import of use<PERSON><PERSON><PERSON>eader with useAuth hook
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { emailTrackingService, EmailTrackingStatistics } from '@/services/emailTrackingService';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pagination } from '@/components/ui/pagination';
import { DatePicker } from '@/components/ui/date-picker';
import { Loader2, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ir<PERSON> } from 'lucide-react';
import { format } from 'date-fns';

// Status badge variants
const statusVariants: Record<string, { color: string; icon: React.ReactNode }> = {
  delivered: { color: 'bg-blue-100 text-blue-800 border-blue-200', icon: <Mail className="h-3 w-3 mr-1" /> },
  opened: { color: 'bg-green-100 text-green-800 border-green-200', icon: <CheckCircle className="h-3 w-3 mr-1" /> },
  clicked: { color: 'bg-purple-100 text-purple-800 border-purple-200', icon: <MousePointer className="h-3 w-3 mr-1" /> },
  bounced: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: <AlertTriangle className="h-3 w-3 mr-1" /> },
  spam: { color: 'bg-red-100 text-red-800 border-red-200', icon: <Ban className="h-3 w-3 mr-1" /> },
  unsubscribed: { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: <XCircle className="h-3 w-3 mr-1" /> },
};

// Stats card component
const StatsCard = ({ title, value, icon, description }: { title: string; value: number; icon: React.ReactNode; description?: string }) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      {icon}
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold">{value}</div>
      {description && <p className="text-xs text-muted-foreground">{description}</p>}
    </CardContent>
  </Card>
);

export const EmailTrackingDashboard = () => {
  // Replace authHeader with token from useAuth
  const { token } = useAuth();
  const { toast } = useToast();
  
  // State for tracking statistics
  const [statistics, setStatistics] = useState<EmailTrackingStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Filter states
  const [filters, setFilters] = useState({
    type: '',
    startDate: null as Date | null,
    endDate: null as Date | null,
    notificationType: '',
    email: '',
    page: 1,
    perPage: 10,
  });

  // Fetch email tracking statistics
  const fetchStatistics = async () => {
    if (!token) {
      console.error('No authentication token available');
      return;
    }

    setLoading(true);
    try {
      const response = await emailTrackingService.getEmailTrackingStatistics(
        token,
        {
          type: filters.type || undefined,
          start_date: filters.startDate ? format(filters.startDate, 'yyyy-MM-dd') : undefined,
          end_date: filters.endDate ? format(filters.endDate, 'yyyy-MM-dd') : undefined,
          notification_type: filters.notificationType || undefined,
          email: filters.email || undefined,
          page: filters.page,
          per_page: filters.perPage,
        }
      );

      if (response.data) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching email tracking statistics:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch email tracking statistics',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStatistics();
  }, [filters.page, filters.perPage]);

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Apply filters
  const applyFilters = () => {
    setFilters(prev => ({ ...prev, page: 1 })); // Reset to first page
    fetchStatistics();
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      type: '',
      startDate: null,
      endDate: null,
      notificationType: '',
      email: '',
      page: 1,
      perPage: 10,
    });
    fetchStatistics();
  };

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <StatsCard 
          title="Total Emails" 
          value={statistics?.total || 0} 
          icon={<Mail className="h-4 w-4 text-muted-foreground" />} 
        />
        <StatsCard 
          title="Delivered" 
          value={statistics?.delivered || 0} 
          icon={<Mail className="h-4 w-4 text-blue-500" />} 
        />
        <StatsCard 
          title="Opened" 
          value={statistics?.opened || 0} 
          icon={<CheckCircle className="h-4 w-4 text-green-500" />} 
        />
        <StatsCard 
          title="Clicked" 
          value={statistics?.clicked || 0} 
          icon={<MousePointer className="h-4 w-4 text-purple-500" />} 
        />
        <StatsCard 
          title="Bounced" 
          value={statistics?.bounced || 0} 
          icon={<AlertTriangle className="h-4 w-4 text-yellow-500" />} 
        />
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Email Tracking Data</CardTitle>
          <CardDescription>Refine the email tracking data based on various criteria</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select 
                value={filters.type} 
                onValueChange={(value) => handleFilterChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                  <SelectItem value="opened">Opened</SelectItem>
                  <SelectItem value="clicked">Clicked</SelectItem>
                  <SelectItem value="bounced">Bounced</SelectItem>
                  <SelectItem value="spam">Spam</SelectItem>
                  <SelectItem value="unsubscribed">Unsubscribed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker
                selected={filters.startDate}
                onSelect={(date: Date | null) => handleFilterChange('startDate', date)}
                placeholderText="Select start date"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker
                selected={filters.endDate}
                onSelect={(date: Date | null) => handleFilterChange('endDate', date)}
                placeholderText="Select end date"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Notification Type</label>
              <Select 
                value={filters.notificationType} 
                onValueChange={(value) => handleFilterChange('notificationType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  <SelectItem value="booking_confirmation">Booking Confirmation</SelectItem>
                  <SelectItem value="booking_reminder">Booking Reminder</SelectItem>
                  <SelectItem value="payment_receipt">Payment Receipt</SelectItem>
                  <SelectItem value="account_verification">Account Verification</SelectItem>
                  <SelectItem value="password_reset">Password Reset</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Email</label>
              <Input
                placeholder="Search by email"
                value={filters.email}
                onChange={(e) => handleFilterChange('email', e.target.value)}
              />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={resetFilters}>Reset Filters</Button>
          <Button onClick={applyFilters}>Apply Filters</Button>
        </CardFooter>
      </Card>

      {/* Email tracking data table */}
      <Card>
        <CardHeader>
          <CardTitle>Email Tracking Data</CardTitle>
          <CardDescription>
            Detailed information about email delivery and engagement
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Email</TableHead>
                    <TableHead>Subject</TableHead>
                    <TableHead>Sent At</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Last Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {statistics?.data && statistics.data.length > 0 ? (
                    statistics.data.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.email}</TableCell>
                        <TableCell>{item.subject}</TableCell>
                        <TableCell>{formatDate(item.sent_at)}</TableCell>
                        <TableCell>
                          <Badge 
                            variant="outline" 
                            className={statusVariants[item.status]?.color || 'bg-gray-100 text-gray-800'}
                          >
                            <span className="flex items-center">
                              {statusVariants[item.status]?.icon}
                              {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                            </span>
                          </Badge>
                        </TableCell>
                        <TableCell>{item.notification_type}</TableCell>
                        <TableCell>
                          {item.status === 'delivered' && formatDate(item.sent_at)}
                          {item.status === 'opened' && formatDate(item.opened_at)}
                          {item.status === 'clicked' && formatDate(item.clicked_at)}
                          {item.status === 'bounced' && formatDate(item.bounced_at)}
                          {item.status === 'spam' && formatDate(item.spam_at)}
                          {item.status === 'unsubscribed' && formatDate(item.unsubscribed_at)}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4">
                        No email tracking data found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {statistics?.pagination && statistics.pagination.total > 0 && (
                <div className="flex items-center justify-between mt-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {(statistics.pagination.current_page - 1) * statistics.pagination.per_page + 1} to {Math.min(statistics.pagination.current_page * statistics.pagination.per_page, statistics.pagination.total)} of {statistics.pagination.total} results
                  </div>
                  <Pagination
                    currentPage={statistics.pagination.current_page}
                    totalItems={statistics.pagination.total}
                    itemsPerPage={statistics.pagination.per_page}
                    onPageChange={(page) => handleFilterChange('page', page)}
                  />
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};